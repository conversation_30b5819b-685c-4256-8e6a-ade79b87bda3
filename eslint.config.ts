// ESLint configuration for Next.js project with <PERSON><PERSON>, React, and Prettier
import { defineConfig } from 'eslint/config';
import tsParser from '@typescript-eslint/parser';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import pluginReact from 'eslint-plugin-react';
import pluginReactHooks from 'eslint-plugin-react-hooks';
// @ts-expect-error - No type definitions available
import pluginJsxA11y from 'eslint-plugin-jsx-a11y';
import pluginImport from 'eslint-plugin-import';
import pluginPrettier from 'eslint-plugin-prettier';
import prettier from 'eslint-config-prettier';
import globals from 'globals';

export default defineConfig([
    pluginReact.configs.flat.recommended,
    {
        files: ['src/**/*.{js,jsx,ts,tsx}', 'app/**/*.{js,jsx,ts,tsx}'],
        ignores: [
            'node_modules/**/*',
            '.next/**/*',
            'out/**/*',
            'dist/**/*',
            'build/**/*',
            'coverage/**/*',
            'public/**/*',
            '*.config.{js,ts}',
            'next.config.js',
        ],
        languageOptions: {
            parser: tsParser,
            parserOptions: {
                ecmaVersion: 'latest',
                sourceType: 'module',
                ecmaFeatures: {
                    jsx: true,
                },
                project: './tsconfig.json',
            },
            globals: {
                ...globals.browser,
                ...globals.node,
            },
        },
        plugins: {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            '@typescript-eslint': tsPlugin as any,
            react: pluginReact,
            'react-hooks': pluginReactHooks,
            'jsx-a11y': pluginJsxA11y,
            import: pluginImport,
            prettier: pluginPrettier,
        },
        settings: {
            react: {
                version: 'detect',
            },
            'import/resolver': {
                typescript: {
                    project: './tsconfig.json',
                },
            },
        },
        rules: {
            // Prettier integration
            'prettier/prettier': 'error',

            // TypeScript rules
            '@typescript-eslint/no-explicit-any': 'warn',
            '@typescript-eslint/no-non-null-assertion': 'warn',
            '@typescript-eslint/explicit-function-return-type': 'off',
            '@typescript-eslint/explicit-module-boundary-types': 'off',
            '@typescript-eslint/consistent-type-imports': 'warn',

            // Import rules
            'import/order': [
                'warn',
                {
                    groups: ['builtin', 'external', 'internal', 'parent', 'sibling', 'index'],
                    'newlines-between': 'always',
                },
            ],
            'import/no-unresolved': 'error',
            'import/prefer-default-export': 'off',

            // React rules
            'react/react-in-jsx-scope': 'off', // React 17+ and Next.js
            'react/function-component-definition': 'off',
            'react/jsx-props-no-spreading': 'off',
            'react/prop-types': 'off', // Using TypeScript

            // React Hooks rules
            'react-hooks/rules-of-hooks': 'error',
            'react-hooks/exhaustive-deps': 'warn',

            // Accessibility rules
            'jsx-a11y/anchor-is-valid': 'warn',

            // General rules
            'no-console': 'warn',
            'no-unused-vars': 'off', // TS handles this
            '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
            'prefer-const': 'error',
            'no-var': 'error',
            'object-shorthand': ['error', 'always'],
            'no-multiple-empty-lines': ['warn', { max: 1 }],
        },
    },
    prettier,
]);