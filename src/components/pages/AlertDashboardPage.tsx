/**
 * AlertDashboardPage Component
 * Reusable page component for the alert dashboard
 */

'use client';

import React from 'react';
import { AlertDashboardLayout } from '@/components/features/alert-dashboard';

interface AlertDashboardPageProps {
    className?: string;
}

/**
 * AlertDashboardPage component
 * Provides the complete alert dashboard page with layout and content
 */
function AlertDashboardPage({ className }: AlertDashboardPageProps) {
    return (
        <AlertDashboardLayout className={className}>
            {/* Alert dashboard content area */}
        </AlertDashboardLayout>
    );
}

export default AlertDashboardPage;
