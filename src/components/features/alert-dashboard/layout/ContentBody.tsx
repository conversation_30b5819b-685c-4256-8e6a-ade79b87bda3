'use client';

import React from 'react';
import { ContentBodyProps } from '../alert-dashboard.types';

/**
 * ContentBody Component
 * Main content area wrapper for the alert dashboard
 */
function ContentBody({ children, className }: ContentBodyProps) {
    return (
        <div
            className={`grid-area-content h-full overflow-y-auto bg-gray-900 p-6 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 ${className || ''}`}>
            {children}
        </div>
    );
}

export default ContentBody;
