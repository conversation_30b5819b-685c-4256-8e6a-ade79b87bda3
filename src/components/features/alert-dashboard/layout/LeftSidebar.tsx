'use client';

import React from 'react';
import { LeftSidebarProps } from '../alert-dashboard.types';

/**
 * LeftSidebar Component
 * Navigation sidebar for the alert dashboard
 */
function LeftSidebar({isMobile = false, className }: LeftSidebarProps) {
    // Generate sidebar classes based on state
    const getSidebarClasses = () => {
        const baseClasses = 'bg-[#0d131ff2] mr-1 transition-all duration-300';
        const gridArea = 'grid-area-sidebar';
        const mobileClasses = isMobile
            ? 'fixed left-0 top-[60px] h-[calc(100vh-60px)] z-[1001] shadow-[2px_0_8px_rgba(0,0,0,0.3)]'
            : '';

        return `${baseClasses} ${gridArea} ${mobileClasses} ${className || ''}`;
    };

    return (
        <div className={getSidebarClasses()}>
            <div className="h-full flex flex-col p-2">
                
            </div>
        </div>
    );
}

export default LeftSidebar;
