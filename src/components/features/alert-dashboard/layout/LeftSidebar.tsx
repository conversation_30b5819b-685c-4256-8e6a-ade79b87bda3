'use client';

import React from 'react';
import { LeftSidebarProps } from '../alert-dashboard.types';
import styles from './AlertDashboardLayout.module.css';

/**
 * LeftSidebar Component
 * Navigation sidebar for the alert dashboard
 */
function LeftSidebar({ collapsed = false, onToggle, isMobile = false, isTablet = false, className }: LeftSidebarProps) {
    return (
        <div className={`${styles['alert-dashboard-sidebar']} ${className || ''}`}>
            <div className="h-full flex flex-col p-2">
                {/* Logo Area */}
                <div className="h-15 border-b border-gray-700 mb-4 flex items-center justify-center text-white font-bold">
                    {!collapsed && <span className="text-sm text-center">Alert Dashboard</span>}
                    {collapsed && <span className="text-lg font-bold">AD</span>}
                </div>

                {/* Navigation Area */}
                <div className="flex-1">
                    <nav className="h-full">
                        <ul className="list-none p-0 m-0 flex flex-col gap-2">
                            <li>
                                <a
                                    href="#"
                                    className="flex items-center gap-3 p-3 text-white no-underline rounded transition-all duration-200 min-h-[44px] bg-gray-700">
                                    <span className="text-xl w-5 text-center flex-shrink-0">🏠</span>
                                    {!collapsed && <span className="text-sm whitespace-nowrap">Dashboard</span>}
                                </a>
                            </li>
                            <li>
                                <a
                                    href="#"
                                    className="flex items-center gap-3 p-3 text-gray-300 no-underline rounded transition-all duration-200 min-h-[44px] hover:bg-gray-600 hover:text-white">
                                    <span className="text-xl w-5 text-center flex-shrink-0">🚨</span>
                                    {!collapsed && <span className="text-sm whitespace-nowrap">Alerts</span>}
                                </a>
                            </li>
                            <li>
                                <a
                                    href="#"
                                    className="flex items-center gap-3 p-3 text-gray-300 no-underline rounded transition-all duration-200 min-h-[44px] hover:bg-gray-600 hover:text-white">
                                    <span className="text-xl w-5 text-center flex-shrink-0">📊</span>
                                    {!collapsed && <span className="text-sm whitespace-nowrap">Analytics</span>}
                                </a>
                            </li>
                            <li>
                                <a
                                    href="#"
                                    className="flex items-center gap-3 p-3 text-gray-300 no-underline rounded transition-all duration-200 min-h-[44px] hover:bg-gray-600 hover:text-white">
                                    <span className="text-xl w-5 text-center flex-shrink-0">⚙️</span>
                                    {!collapsed && <span className="text-sm whitespace-nowrap">Settings</span>}
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    );
}

export default LeftSidebar;
