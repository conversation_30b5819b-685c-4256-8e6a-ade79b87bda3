'use client';

import React, { useState, useEffect } from 'react';
import { AlertDashboardLayoutProps } from '../alert-dashboard.types';
import styles from './AlertDashboardLayout.module.css';
import LeftSidebar from './LeftSidebar';
import TopBar from './TopBar';
import RightDrawer from './RightDrawer';
import ContentBody from './ContentBody';

/**
 * AlertDashboardLayout Component
 * Main layout container for the alert dashboard with responsive grid system
 */
function AlertDashboardLayout({
    children,
    className,
    leftSidebarCollapsed = false,
    rightDrawerOpen = true,
    onLeftSidebarToggle,
    onRightDrawerToggle,
}: AlertDashboardLayoutProps) {
    // Internal state for layout control
    const [sidebarCollapsed, setSidebarCollapsed] = useState(leftSidebarCollapsed);
    const [drawerOpen, setDrawerOpen] = useState(rightDrawerOpen);

    // Handle responsive breakpoints
    const [isMobile, setIsMobile] = useState(false);
    const [isTablet, setIsTablet] = useState(false);

    useEffect(() => {
        const handleResize = () => {
            const width = window.innerWidth;
            setIsMobile(width < 768);
            setIsTablet(width >= 768 && width < 1024);
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Update internal state when props change
    useEffect(() => {
        setSidebarCollapsed(leftSidebarCollapsed);
    }, [leftSidebarCollapsed]);

    useEffect(() => {
        setDrawerOpen(rightDrawerOpen);
    }, [rightDrawerOpen]);

    // Handle sidebar toggle
    const handleSidebarToggle = () => {
        const newCollapsed = !sidebarCollapsed;
        setSidebarCollapsed(newCollapsed);
        onLeftSidebarToggle?.();
    };

    // Handle drawer toggle
    const handleDrawerToggle = () => {
        const newOpen = !drawerOpen;
        setDrawerOpen(newOpen);
        onRightDrawerToggle?.();
    };

    // Generate CSS classes based on state
    const getLayoutClasses = () => {
        const baseClasses = styles['alert-dashboard-layout'];
        const responsiveClasses = isMobile ? styles.mobile : isTablet ? styles.tablet : styles.desktop;
        const sidebarClasses = sidebarCollapsed ? styles['sidebar-collapsed'] : styles['sidebar-expanded'];
        const drawerClasses = drawerOpen ? styles['drawer-open'] : styles['drawer-closed'];

        return `${baseClasses} ${responsiveClasses || ''} ${sidebarClasses || ''} ${drawerClasses || ''} ${className || ''}`;
    };

    return (
        <div className={getLayoutClasses()}>
            {/* Left Sidebar */}
            <LeftSidebar
                collapsed={sidebarCollapsed}
                onToggle={handleSidebarToggle}
                isMobile={isMobile}
                isTablet={isTablet}
            />

            {/* Top Bar */}
            <TopBar onSidebarToggle={handleSidebarToggle} isMobile={isMobile} isTablet={isTablet} />

            {/* Main Content Area */}
            <ContentBody>{children}</ContentBody>

            {/* Right Drawer */}
            <RightDrawer isOpen={drawerOpen} onToggle={handleDrawerToggle} isMobile={isMobile} isTablet={isTablet} />
        </div>
    );
}

export default AlertDashboardLayout;
