'use client';

import React, { useState, useEffect } from 'react';
import { AlertDashboardLayoutProps } from '../alert-dashboard.types';
import LeftSidebar from './LeftSidebar';
import TopBar from './TopBar';
import RightDrawer from './RightDrawer';
import ContentBody from './ContentBody';

/**
 * AlertDashboardLayout Component
 * Main layout container for the alert dashboard with responsive grid system
 */
function AlertDashboardLayout({
    children,
    className,
    leftSidebarCollapsed = false,
    rightDrawerOpen = true,
    onLeftSidebarToggle,
    onRightDrawerToggle,
}: AlertDashboardLayoutProps) {
    // Internal state for layout control
    const [sidebarCollapsed, setSidebarCollapsed] = useState(leftSidebarCollapsed);
    const [drawerOpen, setDrawerOpen] = useState(rightDrawerOpen);

    // Handle responsive breakpoints
    const [isMobile, setIsMobile] = useState(false);
    const [isTablet, setIsTablet] = useState(false);

    useEffect(() => {
        const handleResize = () => {
            const width = window.innerWidth;
            setIsMobile(width < 768);
            setIsTablet(width >= 768 && width < 1024);
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Update internal state when props change
    useEffect(() => {
        setSidebarCollapsed(leftSidebarCollapsed);
    }, [leftSidebarCollapsed]);

    useEffect(() => {
        setDrawerOpen(rightDrawerOpen);
    }, [rightDrawerOpen]);

    // Handle sidebar toggle
    const handleSidebarToggle = () => {
        const newCollapsed = !sidebarCollapsed;
        setSidebarCollapsed(newCollapsed);
        onLeftSidebarToggle?.();
    };

    // Handle drawer toggle
    const handleDrawerToggle = () => {
        const newOpen = !drawerOpen;
        setDrawerOpen(newOpen);
        onRightDrawerToggle?.();
    };

    // Generate dynamic grid template columns based on state
    const getGridTemplateColumns = () => {
        if (isMobile) {
            return '1fr'; // Mobile uses single column
        }

        const sidebarWidth = sidebarCollapsed ? '80px' : '80px';
        const drawerWidth = drawerOpen ? '352px' : '0px';
        return `${sidebarWidth} 1fr ${drawerWidth}`;
    };

    // Generate layout classes
    const getLayoutClasses = () => {
        const baseClasses = 'grid h-screen overflow-hidden bg-gray-900';
        const gridAreas = isMobile ? 'grid-areas-mobile' : 'grid-areas-desktop';
        const gridRows = 'grid-rows-[60px_1fr]';

        return `${baseClasses} ${gridAreas} ${gridRows} ${className || ''}`;
    };

    return (
        <div
            className={getLayoutClasses()}
            style={{
                gridTemplateColumns: getGridTemplateColumns(),
            }}>
            {/* Left Sidebar */}
            <LeftSidebar
                collapsed={sidebarCollapsed}
                onToggle={handleSidebarToggle}
                isMobile={isMobile}
                isTablet={isTablet}
            />

            {/* Top Bar */}
            <TopBar
                onSidebarToggle={handleSidebarToggle}
                onDrawerToggle={handleDrawerToggle}
                isMobile={isMobile}
                isTablet={isTablet}
            />

            {/* Main Content Area */}
            <ContentBody>{children}</ContentBody>

            {/* Right Drawer */}
            <RightDrawer isOpen={drawerOpen} onToggle={handleDrawerToggle} isMobile={isMobile} isTablet={isTablet} />
        </div>
    );
}

export default AlertDashboardLayout;
