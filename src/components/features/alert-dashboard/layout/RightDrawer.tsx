'use client';

import React from 'react';
import { RightDrawerProps } from '../alert-dashboard.types';

/**
 * RightDrawer Component
 * System overview drawer for the alert dashboard
 */
function RightDrawer({ onToggle, className }: RightDrawerProps) {
    return (
        <div className={`grid-area-drawer bg-[#0d131ff2] transition-all duration-300 ${className || ''}`}>
            <div className="h-full flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-4">
                    <h3 className="text-white text-lg font-semibold">System Overview</h3>
                    <button
                        onClick={onToggle}
                        className="bg-transparent border-none text-gray-300 text-xl cursor-pointer p-1 rounded hover:bg-gray-600 hover:text-white transition-all duration-200"
                        aria-label="Close drawer">
                        ✕
                    </button>
                </div>

                {/* Content */}
                <div className="flex-1 p-4 text-gray-200 overflow-y-auto">
                    
                </div>
            </div>
        </div>
    );
}

export default RightDrawer;
