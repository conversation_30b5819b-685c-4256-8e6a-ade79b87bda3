'use client';

import React from 'react';
import { RightDrawerProps } from '../alert-dashboard.types';

/**
 * RightDrawer Component
 * System overview drawer for the alert dashboard with grid layout integration
 */
function RightDrawer({ isOpen = true, onToggle, isMobile = false, className }: RightDrawerProps) {
    // Generate drawer classes based on state
    const getDrawerClasses = () => {
        const baseClasses = 'bg-[#0d131ff2] ml-1 transition-all duration-300';
        const gridArea = 'grid-area-drawer';

        if (isMobile) {
            const mobileClasses =
                'fixed right-0 top-[60px] h-[calc(100vh-60px)] w-80 z-[1000] transition-transform duration-300 ease-in-out';
            const transformClass = isOpen ? 'translate-x-0' : 'translate-x-full';
            return `${baseClasses} ${mobileClasses} ${transformClass} ${className || ''}`;
        }

        return `${baseClasses} ${gridArea} ${className || ''}`;
    };

    return (
        <div className={getDrawerClasses()}>
            <div className="h-full flex flex-col">
                {/* Header with close toggle */}
                <div className="flex items-center justify-between p-4">
                    <h3 className="text-white text-lg font-semibold">System Overview</h3>
                    
                </div>

                
            </div>
        </div>
    );
}

export default RightDrawer;
