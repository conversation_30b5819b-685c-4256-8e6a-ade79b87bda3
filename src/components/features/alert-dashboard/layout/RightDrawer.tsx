'use client';

import React from 'react';
import { RightDrawerProps } from '../alert-dashboard.types';
import styles from './AlertDashboardLayout.module.css';

/**
 * RightDrawer Component
 * System overview drawer for the alert dashboard with grid layout integration
 */
function RightDrawer({ isOpen = true, onToggle, isMobile = false, isTablet = false, className }: RightDrawerProps) {
    return (
        <div className={`${styles['alert-dashboard-drawer']} ${className || ''}`}>
            <div className="h-full flex flex-col">
                {/* Header with close toggle */}
                <div className="flex items-center justify-between p-4 border-b border-gray-600">
                    <h3 className="text-white text-lg font-semibold">System Overview</h3>
                    <button
                        onClick={onToggle}
                        className="bg-transparent border-none text-gray-300 text-xl cursor-pointer p-1 rounded hover:bg-gray-600 hover:text-white transition-all duration-200"
                        aria-label="Close drawer">
                        ✕
                    </button>
                </div>

                {/* Drawer content */}
                <div className="flex-1 p-4 text-gray-200 overflow-y-auto">
                    <div className="space-y-4">
                        <div className="bg-gray-700 p-3 rounded">
                            <h4 className="text-white font-medium mb-2">System Status</h4>
                            <p className="text-sm text-gray-300">All systems operational</p>
                        </div>
                        <div className="bg-gray-700 p-3 rounded">
                            <h4 className="text-white font-medium mb-2">Active Alerts</h4>
                            <p className="text-sm text-gray-300">3 active alerts</p>
                        </div>
                        <div className="bg-gray-700 p-3 rounded">
                            <h4 className="text-white font-medium mb-2">Performance</h4>
                            <p className="text-sm text-gray-300">CPU: 45% | Memory: 62%</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default RightDrawer;
