'use client';

import React from 'react';
import { TopBarProps } from '../alert-dashboard.types';

/**
 * TopBar Component
 * Header for the alert dashboard
 */
function TopBar({ onDrawerToggle, className }: TopBarProps) {
    return (
        <div
            className={`grid-area-topbar bg-[#0d131ff2] border-b border-gray-600 flex items-center h-20 ${className || ''}`}>
            <div className="w-full flex items-center justify-between px-4">
                {/* Left Section - Title */}
                <div className="flex items-center gap-2">
                </div>

                {/* Right Section - Controls */}
                <div className="flex items-center gap-2">
                    <button
                        onClick={onDrawerToggle}
                        className="bg-transparent border border-gray-600 text-gray-300 p-2 rounded hover:bg-gray-600 hover:text-white transition-all duration-200"
                        aria-label="Toggle drawer">
                        ⚙️
                    </button>
                </div>
            </div>
        </div>
    );
}

export default TopBar;
