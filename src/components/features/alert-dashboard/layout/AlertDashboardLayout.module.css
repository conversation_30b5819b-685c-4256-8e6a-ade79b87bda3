/* AlertDashboardLayout Styles */

/* Base layout - simplified for Tailwind components */
.alert-dashboard-layout {
    display: grid;
    grid-template-areas:
        'sidebar topbar'
        'sidebar content';
    grid-template-columns: 60px 1fr;
    grid-template-rows: 60px 1fr;
    height: 100vh;
    overflow: hidden;
    background-color: #1a1a1a;
}

/* Grid areas */
.alert-dashboard-sidebar {
    grid-area: sidebar;
    background-color: #1a1a1a;
    border-right: 1px solid #4b5563;
    transition: width 0.3s ease;
}

.alert-dashboard-topbar {
    grid-area: topbar;
    background-color: #0d131ff2;
    border-bottom: 1px solid #4b5563;
    display: flex;
    align-items: center;
    height: 60px;
}

/* Sidebar states */
.alert-dashboard-layout.sidebar-expanded {
    grid-template-columns: 240px 1fr;
}

.alert-dashboard-layout.sidebar-collapsed {
    grid-template-columns: 60px 1fr;
}

/* Content area - now handled by Tail<PERSON> in ContentBody */

/* Responsive adjustments */
@media (max-width: 768px) {
    .alert-dashboard-layout {
        grid-template-columns: 1fr;
        grid-template-areas:
            'topbar'
            'content';
    }

    .alert-dashboard-sidebar {
        position: fixed;
        left: 0;
        top: 60px;
        height: calc(100vh - 60px);
        width: 60px;
        z-index: 1001;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);
    }

    .alert-dashboard-layout.sidebar-expanded .alert-dashboard-sidebar {
        width: 240px;
    }
}
