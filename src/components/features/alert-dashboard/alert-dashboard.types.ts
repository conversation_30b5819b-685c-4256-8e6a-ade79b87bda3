/**
 * Alert Dashboard Types
 * TypeScript interfaces and types for alert dashboard components
 */

import { ReactNode } from 'react';

/**
 * Props for the main AlertDashboardLayout component
 */
export interface AlertDashboardLayoutProps {
    children?: ReactNode;
    className?: string;
    leftSidebarCollapsed?: boolean;
    rightDrawerOpen?: boolean;
    onLeftSidebarToggle?: () => void;
    onRightDrawerToggle?: () => void;
}

/**
 * Props passed to child components within the alert dashboard
 */
export interface AlertDashboardChildComponentProps {
    collapsed?: boolean; // For sidebar
    isOpen?: boolean; // For drawer
    onToggle?: () => void; // For interactive components
    isMobile?: boolean; // For responsive behavior
    isTablet?: boolean; // For responsive behavior
}

/**
 * Responsive breakpoint states
 */
export interface AlertDashboardResponsiveState {
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
}

/**
 * Layout grid configuration
 */
export interface AlertDashboardGridConfig {
    sidebarWidth: string;
    drawerWidth: string;
    topBarHeight: string;
    collapsedSidebarWidth: string;
}

/**
 * Theme configuration for alert dashboard
 */
export interface AlertDashboardTheme {
    backgroundColor: string;
    borderColor: string;
    sidebarBackground: string;
    topBarBackground: string;
    contentBackground: string;
    drawerBackground: string;
}

/**
 * Props for LeftSidebar component
 */
export interface LeftSidebarProps {
    collapsed?: boolean;
    onToggle?: () => void;
    isMobile?: boolean;
    isTablet?: boolean;
    className?: string;
}

/**
 * Props for TopBar component
 */
export interface TopBarProps {
    onSidebarToggle?: () => void;
    onDrawerToggle?: () => void;
    isMobile?: boolean;
    isTablet?: boolean;
    className?: string;
}

/**
 * Props for RightDrawer component
 */
export interface RightDrawerProps {
    isOpen?: boolean;
    onToggle?: () => void;
    isMobile?: boolean;
    isTablet?: boolean;
    className?: string;
}

/**
 * Props for ContentBody component
 */
export interface ContentBodyProps {
    children?: ReactNode;
    className?: string;
}
